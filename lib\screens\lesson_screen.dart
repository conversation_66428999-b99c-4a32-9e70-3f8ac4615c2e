import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:go_router/go_router.dart';
import 'package:guide_app/theme/app_theme.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class LessonScreen extends StatefulWidget {
  final String lessonId;

  const LessonScreen({
    super.key,
    required this.lessonId,
  });

  @override
  State<LessonScreen> createState() => _LessonScreenState();
}

class _LessonScreenState extends State<LessonScreen> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  bool _isVideoInitialized = false;

  @override
  void initState() {
    super.initState();
    // في التطبيق الحقيقي، سنجلب هذه البيانات من قاعدة البيانات
    if (widget.lessonId == 'lesson1') {
      _initializeVideo();
    }
  }

  Future<void> _initializeVideo() async {
    // استخدم رابط فيديو حقيقي في التطبيق الفعلي
    _videoController = VideoPlayerController.network(
      'https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4',
    );

    await _videoController!.initialize();

    _chewieController = ChewieController(
      videoPlayerController: _videoController!,
      autoPlay: false,
      looping: false,
      aspectRatio: 16 / 9,
      errorBuilder: (context, errorMessage) {
        return Center(
          child: Text(
            'حدث خطأ أثناء تحميل الفيديو: $errorMessage',
            style: const TextStyle(color: Colors.white),
          ),
        );
      },
    );

    setState(() {
      _isVideoInitialized = true;
    });
  }

  @override
  void dispose() {
    if (_videoController != null) {
      _videoController!.dispose();
    }
    if (_chewieController != null) {
      _chewieController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // في التطبيق الحقيقي، سنجلب هذه البيانات من قاعدة البيانات
    final String lessonTitle = widget.lessonId == 'lesson1'
        ? 'الدرس 1: مقدمة في التطبيق'
        : 'الدرس';

    return Scaffold(
      appBar: AppBar(
        title: Text(lessonTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تمت إضافة الدرس إلى المفضلة'),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تمت مشاركة الدرس'),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isVideoInitialized && _chewieController != null)
              AspectRatio(
                aspectRatio: 16 / 9,
                child: Chewie(controller: _chewieController!),
              )
            else if (widget.lessonId == 'lesson1')
              const AspectRatio(
                aspectRatio: 16 / 9,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else
              Container(
                height: 200,
                width: double.infinity,
                color: AppTheme.primaryColor.withOpacity(0.2),
                child: const Center(
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 64,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lessonTitle,
                    style: Theme.of(context).textTheme.displayMedium,
                  ),
                  const SizedBox(height: 16),
                  const Markdown(
                    data: '''
# مقدمة في التطبيق

هذا الدرس يقدم مقدمة شاملة عن التطبيق وكيفية استخدامه بشكل فعال.

## الواجهة الرئيسية

الواجهة الرئيسية للتطبيق تحتوي على العناصر التالية:

- **الشاشة الرئيسية**: تعرض الفصول والدروس المتاحة
- **قائمة الفصول**: تعرض جميع الفصول المتاحة في التطبيق
- **الإعدادات**: تتيح لك تخصيص التطبيق حسب احتياجاتك

## كيفية التنقل

يمكنك التنقل بين الشاشات المختلفة باستخدام:

1. شريط التنقل السفلي
2. الأزرار في الشاشة الرئيسية
3. الضغط على العناصر المختلفة

## تتبع التقدم

يتيح لك التطبيق تتبع تقدمك في كل درس وفصل، ويعرض لك نسبة الإكمال والنقاط التي حصلت عليها.

## الاختبارات القصيرة

بعد كل درس، هناك اختبار قصير للتأكد من فهمك للمحتوى. يجب عليك الإجابة على جميع الأسئلة بشكل صحيح للانتقال إلى الدرس التالي.
''',
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton.icon(
                onPressed: () {
                  // العودة إلى الفصل
                  context.pop();
                },
                icon: const Icon(Icons.arrow_back),
                label: const Text('العودة'),
              ),
              ElevatedButton(
                onPressed: () {
                  // الانتقال إلى الاختبار
                  context.push('/quiz/quiz1');
                },
                child: const Text('بدء الاختبار'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}